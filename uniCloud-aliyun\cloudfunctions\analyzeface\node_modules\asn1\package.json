{"_from": "asn1@~0.2.3", "_id": "asn1@0.2.6", "_inBundle": false, "_integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "_location": "/asn1", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "asn1@~0.2.3", "name": "asn1", "escapedName": "asn1", "rawSpec": "~0.2.3", "saveSpec": null, "fetchSpec": "~0.2.3"}, "_requiredBy": ["/sshpk"], "_resolved": "https://registry.npmmirror.com/asn1/-/asn1-0.2.6.tgz", "_shasum": "0d3a7bb6e64e02a90c0303b31f292868ea09a08d", "_spec": "asn1@~0.2.3", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\sshpk", "author": {"name": "<PERSON><PERSON>", "url": "joyent.com"}, "bugs": {"url": "https://github.com/joyent/node-asn1/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"safer-buffer": "~2.1.0"}, "deprecated": false, "description": "Contains parsers and serializers for ASN.1 (currently BER only)", "devDependencies": {"eslint": "2.13.1", "eslint-plugin-joyent": "~1.3.0", "faucet": "0.0.1", "istanbul": "^0.3.6", "tape": "^3.5.0"}, "homepage": "https://github.com/joyent/node-asn1#readme", "license": "MIT", "main": "lib/index.js", "name": "asn1", "repository": {"type": "git", "url": "git+https://github.com/joyent/node-asn1.git"}, "scripts": {"test": "tape ./test/ber/*.test.js"}, "version": "0.2.6"}