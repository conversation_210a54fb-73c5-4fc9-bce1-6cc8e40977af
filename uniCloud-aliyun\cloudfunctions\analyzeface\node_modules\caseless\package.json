{"_from": "caseless@~0.12.0", "_id": "caseless@0.12.0", "_inBundle": false, "_integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==", "_location": "/caseless", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "caseless@~0.12.0", "name": "caseless", "escapedName": "caseless", "rawSpec": "~0.12.0", "saveSpec": null, "fetchSpec": "~0.12.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/caseless/-/caseless-0.12.0.tgz", "_shasum": "1b681c21ff84033c826543090689420d187151dc", "_spec": "caseless@~0.12.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "devDependencies": {"tape": "^2.10.2"}, "homepage": "https://github.com/mikeal/caseless#readme", "keywords": ["headers", "http", "caseless"], "license": "Apache-2.0", "main": "index.js", "name": "caseless", "repository": {"type": "git", "url": "git+https://github.com/mikeal/caseless.git"}, "scripts": {"test": "node test.js"}, "test": "node test.js", "version": "0.12.0"}