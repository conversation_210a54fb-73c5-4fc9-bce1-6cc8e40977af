{"_from": "har-validator@~5.1.3", "_id": "har-validator@5.1.5", "_inBundle": false, "_integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "_location": "/har-validator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "har-validator@~5.1.3", "name": "har-validator", "escapedName": "har-validator", "rawSpec": "~5.1.3", "saveSpec": null, "fetchSpec": "~5.1.3"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/har-validator/-/har-validator-5.1.5.tgz", "_shasum": "1f0803b9f8cb20c0fa13822df1ecddb36bde1efd", "_spec": "har-validator@~5.1.3", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "bugs": {"url": "https://github.com/ahmadnassri/node-har-validator/issues"}, "bundleDependencies": false, "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "deprecated": "this library is no longer supported", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "devDependencies": {"tap": "^14.10.8"}, "engines": {"node": ">=6"}, "files": ["lib"], "homepage": "https://github.com/ahmadnassri/node-har-validator", "keywords": ["har", "cli", "ajv", "http", "archive", "validate", "validator"], "license": "MIT", "main": "lib/promise.js", "name": "har-validator", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/node-har-validator.git"}, "scripts": {"lint": "npx run-p lint:*", "test": "tap test --no-coverage", "test:coverage": "tap test --coverage-report=lcov --no-browser"}, "version": "5.1.5"}