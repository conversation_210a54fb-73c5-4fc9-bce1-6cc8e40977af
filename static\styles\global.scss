/* 全局样式表 */

/* 在page作用域内定义变量，确保在小程序中生效 */
page {
  --primary-color: #FF80AB; /* 主色调：粉色 */
  --success-color: #9ACD32; /* 成功色：黄绿色 */
  --warning-color: #FF9500; /* 警告色：橙色 */
  --danger-color: #FF6347;  /* 危险色：番茄红 */
  --text-color: #5D4037;    /* 文本色：深棕色 */
  --text-color-secondary: #7D6E6A; /* 次要文本：中棕色 */
  --text-color-light: #A1887F; /* 浅色文本：浅棕色 */
  --background-color: #FFF8E1; /* 背景色：淡黄色 */
  --card-background: #FFFFFF;  /* 卡片背景：白色 */
  --border-color: #FFCDD2;     /* 边框色：淡粉色 */
}

/* 解决Canvas ctx为null问题的通用样式 */
canvas {
  width: 300px;
  height: 300px;
  visibility: visible;
  display: block;
}

/* 容器样式 */
.container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-color: var(--background-color);
}

/* 卡片样式 */
.card {
  width: 90%;
  background-color: var(--card-background);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(255, 128, 171, 0.15);
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 标题样式 */
.page-title {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
  margin: 30px 0;
  text-align: center;
}

/* 上传区域样式 */
.upload-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.upload-area {
  width: 300px;
  height: 300px;
  background-color: var(--card-background);
  border: 2px dashed #FF80AB !important;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:active {
  background-color: #FFF3CD;
  transform: scale(0.98);
}

.upload-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.upload-text {
  color: var(--text-color-secondary);
  font-size: 16px;
}

/* 预览区域样式 */
.preview-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-image {
  width: 100%;
  // height: 100%;
  object-fit: cover; /* 覆盖模式填满容器 */
  margin: 0 auto;
}

.preview-actions {
  width: 90%;
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-top: 20px;
}

/* 按钮样式 */
.button-container {
  width: 90%;
  margin-top: 5px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.btn {
  width: 100%;
  height: 45px;
  line-height: 45px;
  font-size: 16px;
  text-align: center;
  border-radius: 22.5px;
  margin: 0;
  padding: 0;
  border: none !important;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn::after {
  display: none !important;
  border: none !important;
}

.btn:active:not([disabled]) {
  transform: scale(0.98);
  opacity: 0.9;
}

.btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-default {
  background-color: #FFF3CD;
  color: var(--text-color);
  box-shadow: 0 2px 8px rgba(255, 128, 171, 0.1);
  border: 1px solid var(--border-color) !important;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 128, 171, 0.3);
}

.btn-success {
  background-color: var(--success-color) !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(154, 205, 50, 0.3);
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 99, 71, 0.3);
}

/* 提示文字样式 */
.tips {
  margin-top: 20px;
  width: 90%;
  text-align: center;
}

.tips-text {
  color: var(--text-color-light);
  font-size: 12px;
}

/* 通用间距 */
.mt-10 { margin-top: 10px; }
.mb-10 { margin-bottom: 10px; }
.mt-20 { margin-top: 20px; }
.mb-20 { margin-bottom: 20px; }
.mt-30 { margin-top: 30px; }
.mb-30 { margin-bottom: 30px; }

/* 分隔线 */
.divider {
  height: 1px;
  background-color: var(--border-color);
  width: 100%;
  margin: 15px 0;
}

/* 响应式样式 */
@media screen and (max-width: 375px) {
  .upload-area,
  .preview-image,
  .face-frame-container {
    width: 280px;
    height: 280px;
  }
  
  .preview-actions {
    flex-direction: column;
  }
  
  .btn {
    margin-bottom: 10px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-pulse {
  animation: pulse 1.5s infinite;
} 