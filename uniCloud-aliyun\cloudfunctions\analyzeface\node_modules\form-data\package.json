{"_from": "form-data@~2.3.2", "_id": "form-data@2.3.3", "_inBundle": false, "_integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "_location": "/form-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "form-data@~2.3.2", "name": "form-data", "escapedName": "form-data", "rawSpec": "~2.3.2", "saveSpec": null, "fetchSpec": "~2.3.2"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz", "_shasum": "dcce52c05f644f298c6a7ab936bd724ceffbf3a6", "_spec": "form-data@~2.3.2", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "browser": "./lib/browser", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "bundleDependencies": false, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "deprecated": false, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.14", "cross-spawn": "^4.0.2", "eslint": "^3.9.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "2.76.0", "rimraf": "^2.5.4", "tape": "^4.6.2"}, "engines": {"node": ">= 0.12"}, "homepage": "https://github.com/form-data/form-data#readme", "license": "MIT", "main": "./lib/form_data", "name": "form-data", "pre-commit": ["lint", "ci-test", "check"], "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "scripts": {"browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "ci-lint": "is-node-modern 6 && npm run lint || is-node-not-modern 6", "ci-test": "npm run test && npm run browser && npm run report", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "postpublish": "npm run restore-readme", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "pretest": "rimraf coverage test/tmp", "report": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md", "test": "istanbul cover test/run.js", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md"}, "version": "2.3.3"}