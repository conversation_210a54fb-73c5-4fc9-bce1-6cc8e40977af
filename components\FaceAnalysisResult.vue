<template>
  <view class="result-card">
    <view class="face-frame-container">
      <view class="image-container" :class="{'multi-face-mode': facesData.length > 1}">
        <image :src="imageUrl" mode="aspectFit" class="preview-image" @load="onImageLoad" id="previewImage"></image>
        
        <!-- 人脸框和分数徽章 -->
        <view 
          v-for="(face, index) in facesData" 
          :key="'rect-'+index" 
          class="face-rectangle" 
          :style="getFaceRectStyle(face.FaceRectangle)">
        </view>
        
        <view 
          v-for="(face, index) in facesData" 
          :key="'badge-'+index"
          class="score-badge" 
          :class="face.Gender >= 50 ? 'male-style' : 'female-style'" 
          :style="getScoreBadgeStyle(face.FaceRectangle)">
          <view class="badge-content">
            <image 
              :src="face.Gender >= 50 ? '/static/images/male.png' : '/static/images/female.png'" 
              class="gender-icon-image"
              mode="aspectFit">
            </image>
            <text class="score-text">{{ face.Beauty }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 多人场景时不显示下方标签内容 -->
    <block v-if="!isMultiFace">
      <!-- 单人场景下才显示评语和分析项 -->
      <view class="funny-comment" :class="commentClass">
        <text>{{ funnyComment }}</text>
      </view>
      
      <view class="analysis-list">
        <view class="analysis-item">
          <text class="item-label">年龄预测:</text>
          <text class="item-value">{{ age }}岁</text>
        </view>
        
        <view class="analysis-item">
          <text class="item-label">肤质评估:</text>
          <text class="item-value">{{ skinQuality }}</text>
        </view>
        
        <view class="analysis-item">
          <text class="item-label">个性标签:</text>
          <text class="item-value">{{ personalityTag }}</text>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  name: 'FaceAnalysisResult',
  props: {
    imageUrl: {
      type: String,
      default: ''
    },
    beauty: {
      type: Number,
      default: 75
    },
    gender: {
      type: Number,
      default: 50
    },
    age: {
      type: Number,
      default: 25
    },
    facesData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      imageWidth: 0,
      imageHeight: 0,
      originalWidth: 0,
      originalHeight: 0,
      showFaceRect: false,
      effectiveScaleX: 1,
      effectiveScaleY: 1,
      horizontalOffset: 0,
      verticalOffset: 0
    }
  },
  computed: {
    isMultiFace() {
      return this.facesData && this.facesData.length > 1;
    },
    
    score() {
      return this.beauty;
    },
    
    genderClass() {
      return this.gender >= 50 ? 'male-style' : 'female-style';
    },
    
    skinQuality() {
      return this.beauty >= 80 ? '优' : '良';
    },
    
    personalityTag() {
      const isMale = this.gender >= 50;
      const age = this.age;
      const beauty = this.beauty;
      
      if (isMale) {
        if (beauty >= 90) return '行走荷尔蒙';
        if (beauty >= 80) {
          if (age < 25) return '校草本尊';
          if (age < 35) return '霸道总裁';
          return '成功人士';
        }
        if (beauty >= 70) {
          if (age < 28) return '奶油小生';
          if (age < 40) return '中年疲惫';
          return '油腻大叔';
        }
        if (beauty >= 60) {
          if (age < 30) return '梦想青年';
          if (age < 45) return '社畜本畜';
          return '养生达人';
        }
        return age > 40 ? '佛系大叔' : '才高貌浅';
      } else {
        if (beauty >= 90) return '人间尤物';
        if (beauty >= 80) {
          if (age < 25) return '仙女下凡';
          if (age < 35) return '御姐范儿';
          return '优雅贵妇';
        }
        if (beauty >= 70) {
          if (age < 28) return '元气少女';
          if (age < 40) return '女王驾到';
          return '精致阿姨';
        }
        if (beauty >= 60) {
          if (age < 30) return '可盐可甜';
          if (age < 45) return '辣妈一枚';
          return '冻龄女神';
        }
        return age > 40 ? '优雅熟女' : '才高貌浅';
      }
    },
    
    funnyComment() {
      if (this.isMultiFace) {
        const count = this.facesData.length;
        const avgScore = this.beauty;
        const beautyValues = this.facesData.map(face => face.Beauty);
        const highestBeauty = Math.max(...beautyValues);
        const lowestBeauty = Math.min(...beautyValues);
        const difference = highestBeauty - lowestBeauty;
        
        if (difference >= 20) {
          if (count === 2) {
            return `这对CP颜值差距有点大，一个是天上的星星，一个是海里的鱼...`;
          } else if (count === 3) {
            return `三人行，必有颜值担当，也必有颜值背锅侠！看出谁是谁了吗？`;
          } else if (count === 4) {
            return `当一个颜值爆表的人和几个路人站一起，这张照片就很有故事了...`;
          } else {
            return `这群人里有几个真正的大神，也有几个混脸熟蹭照片的...`;
          }
        } else if (difference >= 10) {
          if (avgScore >= 75) {
            return `你们${count}个人的合照，都很养眼，但总有人抢了C位...`;
          } else {
            return `${count}个人的标准社交照，一个微笑，一个营业，一个放空...`;
          }
        } else {
          if (avgScore >= 85) {
            return `这${count}个人都是同一个美颜app出来的吧？滤镜开太大了！`;
          } else if (avgScore >= 75) {
            return `${count}人合照，颜值都在线，关系户都没有，真难得！`;
          } else if (avgScore >= 65) {
            return `${count}个好友的日常聚会照，气氛比颜值更重要！`;
          } else {
            return `${count}个人的友谊像这张照片，不是靠颜值维系的...`;
          }
        }
      }
      
      const score = this.beauty;
      if (score >= 90) return '这颜值，怕是天使看了都要吃醋！简直违反宇宙法则！';
      if (score >= 85) return '你这颜值，是不是每天被万人暗恋的感觉？';
      if (score >= 80) return '这脸蛋，估计连月亮都要躲进云里偷看你！';
      if (score >= 75) return '你的颜值是不是充了VIP？这种级别我只在杂志上见过！';
      if (score >= 70) return '这颜值，让多少化妆品公司破产的节奏啊！';
      if (score >= 65) return '你的脸还行吧，放在朋友圈勉强不用P图...';
      if (score >= 60) return '这颜值嘛...也就只能靠性格和才华撑场面了！';
      if (score >= 55) return '颜值一般，但气质还是能打个六折的！';
      if (score >= 50) return '长得...挺有个性的，记忆点很强！';
      return '这张脸放在人堆里，绝对是...找不到的那种！';
    },
    
    commentClass() {
      return this.beauty >= 70 ? 'high-score-comment' : 'low-score-comment';
    }
  },
  methods: {
    onImageLoad() {
      const query = uni.createSelectorQuery().in(this);
      query.select('#previewImage').boundingClientRect(data => {
        if (data) {
          this.imageWidth = data.width;
          this.imageHeight = data.height;
          this.recalculateScaling();
          this.showFaceRect = true;
        }
      }).exec();
    },
    
    recalculateScaling() {
      if (!this.originalWidth || !this.originalHeight) {
        uni.getImageInfo({
          src: this.imageUrl,
          success: (res) => {
            this.originalWidth = res.width;
            this.originalHeight = res.height;
            this.calculateScalingAndOffset();
          },
          fail: () => {
            this.originalWidth = 300;
            this.originalHeight = 400;
            this.calculateScalingAndOffset();
          }
        });
      } else {
        this.calculateScalingAndOffset();
      }
    },
    
    calculateScalingAndOffset() {
      const origRatio = this.originalWidth / this.originalHeight;
      const renderedRatio = this.imageWidth / this.imageHeight;
      
      if (origRatio > renderedRatio) {
        const actualHeight = this.imageWidth / origRatio;
        this.verticalOffset = (this.imageHeight - actualHeight) / 2;
        this.horizontalOffset = 0;
        this.effectiveScaleX = this.imageWidth / this.originalWidth;
        this.effectiveScaleY = this.effectiveScaleX;
      } else {
        const actualWidth = this.imageHeight * origRatio;
        this.horizontalOffset = (this.imageWidth - actualWidth) / 2;
        this.verticalOffset = 0;
        this.effectiveScaleY = this.imageHeight / this.originalHeight;
        this.effectiveScaleX = this.effectiveScaleY;
      }
    },
    
    getFaceRectStyle(faceRect) {
      if (!faceRect || !this.imageWidth || !this.imageHeight) {
        return {};
      }
      
      const scaleX = this.effectiveScaleX || this.imageWidth / this.originalWidth;
      const scaleY = this.effectiveScaleY || this.imageHeight / this.originalHeight;
      
      return {
        left: (faceRect.left * scaleX + (this.horizontalOffset || 0)) + 'px',
        top: (faceRect.top * scaleY + (this.verticalOffset || 0)) + 'px',
        width: (faceRect.width * scaleX) + 'px',
        height: (faceRect.height * scaleY) + 'px'
      };
    },
    
    getScoreBadgeStyle(faceRect) {
      if (!faceRect || !this.imageWidth || !this.imageHeight) {
        return {};
      }
      
      const scaleX = this.effectiveScaleX || this.imageWidth / this.originalWidth;
      const scaleY = this.effectiveScaleY || this.imageHeight / this.originalHeight;
      
      const left = faceRect.left * scaleX + (this.horizontalOffset || 0) + (faceRect.width * scaleX / 2);
      const top = faceRect.top * scaleY + (this.verticalOffset || 0) - 40;
      
      return {
        left: left + 'px',
        top: top + 'px',
        transform: 'translate(-50%, 0)'
      };
    }
  }
}
</script>

<style>
.result-card {
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
}

.image-container {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 12px;
}

.face-rectangle {
  position: absolute;
  border: 2px solid #ffffff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  pointer-events: none;
  z-index: 10;
}

.score-badge {
  position: absolute;
  color: white;
  border-radius: 20px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  z-index: 11;
  padding: 0 10px;
  height: 30px;
  background-color: rgba(255, 105, 180, 0.85);
}

.badge-content {
  height: 100%;
  display: flex;
  align-items: center;
}

.gender-icon-image {
  width: 20px;  
  height: 20px; 
  margin-right: 3px;
}

.score-text {
  font-size: 16px;
  font-weight: bold;
}

.male-style {
  background-color: rgba(104, 177, 255, 0.85);
}

.female-style {
  background-color: rgba(255, 106, 134, 0.85);
}

.funny-comment {
  text-align: center;
  margin: 15px auto 20px;
  padding: 12px;
  font-size: 18px;
  font-weight: bold;
  line-height: 1.5;
  border-radius: 12px;
}

.high-score-comment {
  color: #FF9500;
  background-color: rgba(255, 149, 0, 0.1);
}

.low-score-comment {
  color: #5856D6;
  background-color: rgba(88, 86, 214, 0.1);
}

.analysis-list {
  margin-top: 20px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
}

.analysis-item {
  background-color: transparent;
  border-radius: 18px;
  padding: 8px 16px;
  display: inline-flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.item-label {
  color: #999;
  font-size: 14px;
  margin-right: 8px;
}

.item-value {
  color: #333;
  font-weight: bold;
  font-size: 16px;
}
</style> 