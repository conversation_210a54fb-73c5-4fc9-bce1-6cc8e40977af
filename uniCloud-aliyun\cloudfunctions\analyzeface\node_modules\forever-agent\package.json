{"_from": "forever-agent@~0.6.1", "_id": "forever-agent@0.6.1", "_inBundle": false, "_integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==", "_location": "/forever-agent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "forever-agent@~0.6.1", "name": "forever-agent", "escapedName": "forever-agent", "rawSpec": "~0.6.1", "saveSpec": null, "fetchSpec": "~0.6.1"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/forever-agent/-/forever-agent-0.6.1.tgz", "_shasum": "fbc71f0c41adeb37f96c577ad1ed42d8fdacca91", "_spec": "forever-agent@~0.6.1", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "bugs": {"url": "https://github.com/mikeal/forever-agent/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "HTTP Agent that keeps socket connections alive between keep-alive requests. Formerly part of mikeal/request, now a standalone module.", "devDependencies": {}, "engines": {"node": "*"}, "homepage": "https://github.com/mikeal/forever-agent#readme", "license": "Apache-2.0", "main": "index.js", "name": "forever-agent", "optionalDependencies": {}, "repository": {"url": "git+https://github.com/mikeal/forever-agent.git"}, "version": "0.6.1"}