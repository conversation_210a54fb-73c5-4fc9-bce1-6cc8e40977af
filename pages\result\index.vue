<template>
	<view class="container">
		<view class="card">
			<view class="face-frame-container">
				<view class="image-container" :class="{'multi-face-mode': facesData.length > 1}">
					<image :src="imageUrl" mode="aspectFit" class="preview-image" @load="onImageLoad" id="previewImage"></image>
					
					<!-- 添加图片尺寸调试信息 -->
					<view class="debug-info" v-if="showDebug">
						<text>图片尺寸: {{imageWidth}} x {{imageHeight}}</text>
						<text>原始尺寸: {{originalWidth}} x {{originalHeight}}</text>
						<text>缩放比例: {{imageWidth/originalWidth}} x {{imageHeight/originalHeight}}</text>
					</view>
					
					<!-- 循环渲染人脸框 -->
					<view 
						v-for="(face, index) in facesData" 
						:key="'rect-'+index" 
						class="face-rectangle" 
						:style="getFaceRectStyle(face.FaceRectangle)">
					</view>
					
					<!-- 单独循环渲染颜值分数 -->
					<view 
						v-for="(face, index) in facesData" 
						:key="'badge-'+index"
						class="score-badge" 
						:class="face.Gender >= 50 ? 'male-style' : 'female-style'" 
						:style="getScoreBadgeStyle(face.FaceRectangle)">
					<view class="badge-content">
						<image 
								:src="face.Gender >= 50 ? '/static/images/male.png' : '/static/images/female.png'" 
							class="gender-icon-image"
								mode="aspectFit">
							</image>
							<text class="score-text">{{ face.Beauty }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 显示检测到的人脸数量 -->
			<view class="multi-face-tip" v-if="isMultiFace">
				<text>检测到 {{ facesData.length }} 个人脸</text>
			</view>
			
			<!-- 评价始终显示，无论单人还是多人 -->
			<view class="funny-comment" :class="commentClass">
				<text>{{ funnyComment }}</text>
			</view>
			
			<!-- 分析项仅在单人脸时显示 -->
			<view class="analysis-list" v-if="!isMultiFace">
				<view class="analysis-item" v-for="(item, index) in analysisItems" :key="index">
					<text class="item-label">{{ item.label }}:</text>
					<text class="item-value">{{ item.value }}</text>
				</view>
			</view>
		</view>
		
		<!-- 保存状态提示 -->
		<view class="save-status" v-if="saveStatus">
			<text>{{ saveStatus }}</text>
		</view>
		
		<view class="button-container">
			<button class="btn btn-default" @tap="goBack">试试其他照片</button>
			<button class="btn btn-primary" open-type="share">分享结果</button>
		</view>
		
		<view class="tips">
			<text class="tips-text">* 分析结果仅供参考娱乐</text>
		</view>
		
		<!-- 添加调试信息帮助验证 -->
		<view class="debug-info" v-if="false">
			<text>原始性别值: {{ gender }}</text>
			<text>解析为: {{ gender >= 50 ? '男' : '女' }}</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 基本数据
			imageUrl: '',
			score: 75,
			
			// 分析结果
			beauty: 75,
			gender: 50,
			age: 25,
			
			// 存储所有人脸数据
			facesData: [],
			
			// 图片尺寸信息
			imageWidth: 0,
			imageHeight: 0,
			originalWidth: 0,
			originalHeight: 0,
			
			// 简化这部分，确保基础功能能正常显示
			isUsingMockData: false,
			showDebug: false,
			saveStatus: ''
		}
	},
	computed: {
		// 保持基础计算属性
		genderIconPath() {
			return this.gender >= 50 ? '/static/images/male.png' : '/static/images/female.png';
		},
		genderText() {
			return this.gender >= 50 ? '男' : '女';
		},
		skinQuality() {
			return this.beauty >= 80 ? '优' : '良';
		},
		// 根据性别、年龄、颜值生成更诙谐的综合标签
		personalityTag() {
			const isMale = this.gender >= 50;
			const age = this.age;
			const beauty = this.beauty;
			
			// 根据不同条件组合生成有趣的标签
			if (isMale) {
				// 男性标签
				if (beauty >= 90) return '行走荷尔蒙';
				if (beauty >= 80) {
					if (age < 25) return '校草本尊';
					if (age < 35) return '霸道总裁';
					return '成功人士';
				}
				if (beauty >= 70) {
					if (age < 28) return '奶油小生';
					if (age < 40) return '中年疲惫';
					return '油腻大叔';
				}
				if (beauty >= 60) {
					if (age < 30) return '梦想青年'; // 替换"沙雕追梦人"
					if (age < 45) return '社畜本畜';
					return '养生达人';
				}
				return age > 40 ? '佛系大叔' : '才高貌浅'; // 替换"明天的自己"和"靠才华吃饭"
			} else {
				// 女性标签
				if (beauty >= 90) return '人间尤物';
				if (beauty >= 80) {
					if (age < 25) return '仙女下凡';
					if (age < 35) return '御姐范儿';
					return '优雅贵妇';
				}
				if (beauty >= 70) {
					if (age < 28) return '元气少女';
					if (age < 40) return '女王驾到'; // 替换"职场女强人"
					return '精致阿姨';
				}
				if (beauty >= 60) {
					if (age < 30) return '可盐可甜';
					if (age < 45) return '辣妈一枚'; // 替换"带娃辣妈"
					return '冻龄女神'; // 替换"抗老天使"
				}
				return age > 40 ? '优雅熟女' : '才高貌浅'; // 替换"贤内助"
			}
		},
		// 判断是否为多人场景
		isMultiFace() {
			return this.facesData && this.facesData.length > 1;
		},
		
		// 多人或单人的评价
		funnyComment() {
			// 多人场景评语
			if (this.isMultiFace) {
				const count = this.facesData.length;
				
				// 计算颜值差异和最高/最低颜值
				const beautyValues = this.facesData.map(face => face.Beauty);
				const highestBeauty = Math.max(...beautyValues);
				const lowestBeauty = Math.min(...beautyValues);
				const difference = Math.round(highestBeauty - lowestBeauty);
				
				// 计算平均颜值
				const avgBeauty = Math.round(beautyValues.reduce((sum, val) => sum + val, 0) / beautyValues.length);
				
				console.log(`多人脸颜值差异: ${difference}, 最高: ${highestBeauty}, 最低: ${lowestBeauty}, 平均: ${avgBeauty}`);
				
				// 根据差异提供搞笑评价，不显示具体分差，不提及位置和具体数量
				if (difference >= 30) {
					// 极大差异
					if (count === 2) {
						return `好吧，不知道该说什么了！`;
					} else if (count === 3) {
						return `合影定律：有人C位出道，有人只能当背景板！`;
					} else if (count === 4) {
						return `这组合简直是颜值过山车，高低起伏太刺激了！`;
					} else {
						return `群像照定律：看过之后只记得最美和最丑的脸！`;
					}
				} else if (difference >= 20) {
					// 明显差异
					if (count === 2) {
						return `这对CP合照，仿佛在演《美女与野兽》真人版！`;
					} else if (count === 3) {
						return `三人照定律：总有人负责貌美如花，也有人负责惊吓路人！`;
					} else {
						return `这合照就像选秀节目，同一舞台差距有点大啊！`;
					}
				} else if (difference >= 10) {
					// 中等差异
					if (count === 2) {
						return `双人照：一个靠脸吃饭，一个靠才华生存...`;
					} else if (count === 3) {
						return `三人照有点像综艺节目：颜值担当、实力担当、搞笑担当！`;
					} else {
						return `合照中的颜值差异，就像考试成绩，总有人拉低平均分...`;
					}
				} else {
					// 差异不大，根据平均分评价
					if (avgBeauty >= 90) {
						// 全员超高颜值
						if (count === 2) {
							return `这颜值组合，让人怀疑是在看偶像剧片场照！`;
						} else {
							return `这组合颜值爆表！建议挂在整容医院做成功案例展示！`;
						}
					} else if (avgBeauty >= 85) {
						// 全员高颜值
						if (count === 2) {
							return `这对神仙CP，疑似从漫画里走出来的！请问是来凡间游玩吗？`;
						} else {
							return `这组颜值，不去当明星真是屏幕前亿万网友的损失！`;
						}
					} else if (avgBeauty >= 75) {
						// 全员中上颜值
						return `这合照就像精品连锁店，颜值标准统一，品质有保障！`;
					} else if (avgBeauty >= 65) {
						// 全员中等颜值
						return `友谊的本质：臭味相投，颜值相当！这默契值得炫耀！`;
					} else if (avgBeauty >= 55) {
						// 全员中下颜值
						return `这组合证明了：真正的友谊不看脸，都在同一个颜值段位才最舒适！`;
					} else {
						// 全员低颜值
						if (count === 2) {
							return `能找到颜值如此匹配的伙伴，这就是命运的安排啊！`;
						} else {
							return `这组合照告诉我们：长相不重要，重要的是找到一群志同道合的朋友！`;
						}
					}
				}
			}
			
			// 单人场景评语 - 调整阈值使其更符合实际分数分布
			const score = this.beauty;
			if (score >= 90) return '这颜值，怕是天使看了都要吃醋！简直违反宇宙法则！';
			if (score >= 85) return '你这颜值，是不是每天被万人暗恋的感觉？';
			if (score >= 80) return '这脸蛋，估计连月亮都要躲进云里偷看你！';
			if (score >= 75) return '你的颜值是不是充了VIP？这种级别我只在杂志上见过！';
			if (score >= 70) return '这颜值，让多少化妆品公司破产的节奏啊！';
			if (score >= 65) return '你的脸还行吧，放在朋友圈勉强不用P图...';
			if (score >= 60) return '这颜值嘛...也就只能靠性格和才华撑场面了！';
			if (score >= 55) return '颜值一般，但气质还是能打个六折的！';
			if (score >= 50) return '长得...挺有个性的，记忆点很强！';
			return '这张脸放在人堆里，绝对是...找不到的那种！';
		},
		analysisItems() {
			const hasManyFaces = this.facesData && this.facesData.length > 1;
			
			// 多人脸场景下直接返回空数组，不显示分析项
			if (hasManyFaces) {
				console.log('多人脸场景，不显示分析项');
				return [];
			}
			
			// 只处理单人脸场景
			const items = [];
			items.push({ label: '年龄预测', value: this.age + '岁' });
			items.push({ label: '肤质评估', value: this.skinQuality });
			items.push({ label: '个性标签', value: this.personalityTag });
			
			return items;
		},
		genderClass() {
			return this.gender >= 50 ? 'male-style' : 'female-style';
		},
		commentClass() {
			return this.beauty >= 70 ? 'high-score-comment' : 'low-score-comment';
		},
		// 计算人脸框样式
		faceRectStyle() {
			if (!this.faceRectangle || !this.imageWidth || !this.imageHeight) {
				return {};
			}
			
			console.log('计算人脸框样式:', {
				faceRect: this.faceRectangle,
				imgWidth: this.imageWidth,
				imgHeight: this.imageHeight,
				origWidth: this.originalWidth,
				origHeight: this.originalHeight
			});
			
			// 计算比例 - 确保能适应屏幕大小的图片
			const scaleX = this.imageWidth / this.originalWidth;
			const scaleY = this.imageHeight / this.originalHeight;
			
			// 计算位置和大小
			const style = {
				left: (this.faceRectangle.left * scaleX) + 'px',
				top: (this.faceRectangle.top * scaleY) + 'px',
				width: (this.faceRectangle.width * scaleX) + 'px',
				height: (this.faceRectangle.height * scaleY) + 'px'
			};
			
			console.log('生成的人脸框样式:', style);
			return style;
		},
		
		// 计算分数徽章样式
		scoreBadgeStyle() {
			if (!this.faceRectangle || !this.imageWidth || !this.imageHeight) {
				return {};
			}
			
			const scaleX = this.imageWidth / this.originalWidth;
			const scaleY = this.imageHeight / this.originalHeight;
			
			// 计算位置 - 确保显示在人脸上方
			const left = (this.faceRectangle.left + this.faceRectangle.width/2) * scaleX;
			const top = (this.faceRectangle.top * scaleY) - 50; // 上移50px
			
			return {
				left: left + 'px',
				top: top + 'px',
				transform: 'translate(-50%, 0)'
			};
		},
		
		// 确保此函数在methods对象中正确声明
		getBadgeClass(faceRect) {
			if (!faceRect) return 'badge-medium';
			
			// 使用CSS类实现不同尺寸，而不是直接计算样式
			const width = faceRect.width || 100;
			
			if (width < 60) return 'badge-small';
			if (width < 100) return 'badge-medium';
			if (width > 150) return 'badge-large';
			return 'badge-medium';
		},
		// 计算颜值差异
		beautyDifference() {
			if (!this.facesData || this.facesData.length <= 1) return 0;
			
			const beautyValues = this.facesData.map(face => face.Beauty);
			const highestBeauty = Math.max(...beautyValues);
			const lowestBeauty = Math.min(...beautyValues);
			
			return Math.round(highestBeauty - lowestBeauty);
		}
	},
	onLoad(options) {
		try {
			console.log('结果页面加载，参数:', options);
			
			// 如果传入了id参数，从数据库获取数据
			if (options && options.id) {
				console.log('检测到ID参数，从数据库获取数据:', options.id);
				this.loadFromDatabase(options.id);
				return;
			}
			
			// 获取全局数据
			const app = getApp();
			if (app && app.globalData && app.globalData.analysisResult) {
				console.log('从全局数据获取结果');
				
				// 设置图片URL - 已经是云存储URL
				if (app.globalData.analysisResult.imageUrl) {
					this.imageUrl = app.globalData.analysisResult.imageUrl;
					console.log('设置图片URL:', this.imageUrl);
				}
				
				// 从数据中提取人脸信息
				const faceData = app.globalData.analysisResult.data;
				console.log('人脸数据:', JSON.stringify(faceData));
				
				if (faceData) {
					// 设置基本数据
					this.beauty = faceData.Beauty || 75;
					this.gender = faceData.Gender || 50;
					this.age = faceData.Age || 25;
					this.score = this.beauty;
					
					// 设置人脸框数据
					if (faceData.FaceRectangle) {
						console.log('人脸框数据:', JSON.stringify(faceData.FaceRectangle));
						this.faceRectangle = faceData.FaceRectangle;
					}
					
					// 处理多个人脸
					if (faceData.faces && faceData.faces.length > 0) {
						console.log('检测到多个人脸:', faceData.faces.length);
						this.facesData = faceData.faces;
						
						// 多人脸情况下，使用第一个人脸作为主数据，不计算平均值
						if (this.facesData.length > 1) {
							console.log('多人脸情况下使用第一个人脸作为主数据，不计算平均值');
							const firstFace = this.facesData[0];
							console.log('第一个人脸数据:', JSON.stringify(firstFace));
							this.beauty = firstFace.Beauty;
							this.gender = firstFace.Gender;
							this.age = firstFace.Age;
							this.score = this.beauty;
						}
					} else if (faceData.FaceRectangle) {
						// 单个人脸数据
						this.facesData = [{
							Beauty: this.beauty,
							Gender: this.gender,
							Age: this.age,
							FaceRectangle: faceData.FaceRectangle
						}];
					}
				}
				
				// 获取图片信息
				setTimeout(() => {
					uni.getImageInfo({
						src: this.imageUrl,
						success: (res) => {
							console.log('获取图片信息成功:', res);
							this.originalWidth = res.width;
							this.originalHeight = res.height;
						},
						fail: (err) => {
							console.error('获取图片信息失败:', err);
							// 使用默认值
							this.originalWidth = 300;
							this.originalHeight = 400;
						}
					});
				}, 300);
				
				return;
			}
			
			// 没有数据使用测试数据
			console.log('没有找到有效数据，使用测试数据');
			this.useMockData();
			
		} catch (error) {
			console.error('数据处理异常:', error);
			this.useMockData();
		}
	},
	methods: {
		// 图片加载完成处理
		onImageLoad(e) {
			console.log('图片加载完成');
			
			// 优先获取原始图片尺寸
			uni.getImageInfo({
				src: this.imageUrl,
				success: (imgInfo) => {
					console.log('原始图片信息:', imgInfo);
					this.originalWidth = imgInfo.width;
					this.originalHeight = imgInfo.height;
					
					// 然后获取渲染后的尺寸
					this.getRenderedImageSize();
				},
				fail: (err) => {
					console.error('获取图片信息失败:', err);
					// 使用默认尺寸
					this.originalWidth = 300;
					this.originalHeight = 400;
					this.getRenderedImageSize();
				}
			});
		},
		
		// 获取渲染后的图片尺寸
		getRenderedImageSize() {
			const query = uni.createSelectorQuery().in(this);
			query.select('#previewImage').boundingClientRect(data => {
				console.log('渲染后图片尺寸:', data);
				if (data) {
					this.imageWidth = data.width;
					this.imageHeight = data.height;
			
					// 重新计算比例
					this.recalculateScaling();
					
					// 强制更新视图
					this.$forceUpdate();
				}
			}).exec();
		},
		
		// 重新计算缩放比例和偏移量
		recalculateScaling() {
			// 处理aspectFit模式下的实际缩放
			const origRatio = this.originalWidth / this.originalHeight;
			const renderedRatio = this.imageWidth / this.imageHeight;
			
			// 修正缩放比例，考虑aspectFit模式
			if (origRatio > renderedRatio) {
				// 宽度受限
				const actualHeight = this.imageWidth / origRatio;
				this.verticalOffset = (this.imageHeight - actualHeight) / 2;
				this.horizontalOffset = 0;
				this.effectiveScaleX = this.imageWidth / this.originalWidth;
				this.effectiveScaleY = this.effectiveScaleX; // 保持比例
			} else {
				// 高度受限
				const actualWidth = this.imageHeight * origRatio;
				this.horizontalOffset = (this.imageWidth - actualWidth) / 2;
				this.verticalOffset = 0;
				this.effectiveScaleY = this.imageHeight / this.originalHeight;
				this.effectiveScaleX = this.effectiveScaleY; // 保持比例
			}
			
			console.log('有效缩放比例:', this.effectiveScaleX, this.effectiveScaleY);
			console.log('偏移量:', this.horizontalOffset, this.verticalOffset);
		},
		
		// 修正人脸框样式计算
		getFaceRectStyle(faceRect) {
			if (!faceRect || !this.imageWidth || !this.imageHeight) {
				return {};
			}
			
			// 使用修正后的缩放比例
			const scaleX = this.effectiveScaleX || this.imageWidth / this.originalWidth;
			const scaleY = this.effectiveScaleY || this.imageHeight / this.originalHeight;
			
			// 计算位置和大小，考虑偏移量
			return {
				left: (faceRect.left * scaleX + (this.horizontalOffset || 0)) + 'px',
				top: (faceRect.top * scaleY + (this.verticalOffset || 0)) + 'px',
				width: (faceRect.width * scaleX) + 'px',
				height: (faceRect.height * scaleY) + 'px'
			};
		},
		
		// 获取分数徽章样式
		getScoreBadgeStyle(faceRect) {
			if (!faceRect || !this.imageWidth || !this.imageHeight) {
				return {};
			}
			
			// 使用修正后的缩放比例
			const scaleX = this.effectiveScaleX || this.imageWidth / this.originalWidth;
			const scaleY = this.effectiveScaleY || this.imageHeight / this.originalHeight;
			
			// 计算位置，始终显示在人脸上方
			const left = faceRect.left * scaleX + (this.horizontalOffset || 0) + (faceRect.width * scaleX / 2);
			const top = faceRect.top * scaleY + (this.verticalOffset || 0) - 35; // 固定在上方
			
			// 不再切换到下方显示，避免重叠
			return {
				left: left + 'px',
				top: top + 'px',
				transform: 'translate(-50%, 0)'
			};
		},
		
		// 根据性别获取对应的类名
		getGenderClass(gender) {
			return gender >= 50 ? 'male-style' : 'female-style';
		},
		
		// 根据性别获取对应图标
		getGenderIconPath(gender) {
			return gender >= 50 ? '/static/images/male.png' : '/static/images/female.png';
		},
		
		// 获取主要人脸(面积最大的人脸)
		getMainFace(faces) {
			if (!faces || faces.length === 0) return null;
			if (faces.length === 1) return faces[0];
			
			return faces.reduce((main, face) => {
				const currentArea = face.FaceRectangle ? face.FaceRectangle.width * face.FaceRectangle.height : 0;
				const mainArea = main.FaceRectangle ? main.FaceRectangle.width * main.FaceRectangle.height : 0;
				return currentArea > mainArea ? face : main;
			}, faces[0]);
		},
		
		// 使用测试数据
		useMockData() {
			this.isUsingMockData = true;
			this.beauty = 85;
			this.score = this.beauty;
			this.gender = 75;
			this.age = 28;
			
			// 测试用人脸数据
			this.facesData = [
				{
					Beauty: 85,
					Gender: 75,
					Age: 28,
					FaceRectangle: {
						top: 100,
						left: 100,
						width: 150,
						height: 150
					}
				}
			];
			
			this.originalWidth = 300;
			this.originalHeight = 400;
		},
		
		// 返回首页
		goBack() {
			uni.redirectTo({
				url: '/pages/index/index'
			});
		},
		
		// 分享功能
		onShareAppMessage() {
			return {
				title: `我的颜值得分：${this.score}分，快来测测你的吧！`,
				path: '/pages/index/index',
				imageUrl: this.imageUrl
			};
		},
		
		// 添加从数据库加载数据的方法
		async loadFromDatabase(id) {
			try {
				console.log('开始从数据库加载数据，ID:', id);
				const db = uniCloud.database();
				const collection = db.collection('face_analysis');
				
				const res = await collection.doc(id).get();
				console.log('数据库查询结果:', res);
				
				if (res.result && res.result.data && res.result.data.length > 0) {
					const record = res.result.data[0];
					console.log('获取到的记录详情:', JSON.stringify(record));
					
					// 设置图片URL - 从cloud_file_id字段获取
					this.imageUrl = record.cloud_file_id || '';
					console.log('设置图片URL:', this.imageUrl);
					
					// 从analysis_result字段获取分析数据
					const analysisResult = record.analysis_result;
					if (analysisResult) {
						console.log('解析分析结果:', JSON.stringify(analysisResult));
						
						// 详细打印多人脸数据，方便调试
						if (analysisResult.faces_count && analysisResult.faces_count > 1) {
							console.log('检测到多人脸数量:', analysisResult.faces_count);
							console.log('人脸位置数据:', JSON.stringify(analysisResult.face_rectangles || []));
							console.log('完整人脸数据:', JSON.stringify(analysisResult.faces_data || []));
						}
						
						// 提取人脸信息
						this.beauty = analysisResult.score || 75;
						this.gender = typeof analysisResult.gender === 'string' ?
							(analysisResult.gender === "男" ? 80 : 20) :
							(analysisResult.gender || 50); // 兼容新旧数据格式
						this.age = analysisResult.age || 25;
						this.score = this.beauty;
						
						// 创建人脸数据数组 - 使用存储的FaceRectangle
						this.facesData = [];
						const faceCount = analysisResult.faces_count || 1;
						console.log('处理人脸数量:', faceCount);
						
						// 使用存储的人脸位置信息
						if (analysisResult.face_rectangles && analysisResult.face_rectangles.length > 0) {
							// 有存储人脸位置信息
							console.log('使用存储的人脸位置信息，数量:', analysisResult.face_rectangles.length);
							
							// 优先使用数据库中存储的完整人脸数据
							const hasSavedFacesData = analysisResult.faces_data && 
													 analysisResult.faces_data.length === analysisResult.face_rectangles.length;
							
							if (hasSavedFacesData) {
								console.log('使用数据库中保存的完整人脸数据');
							}
							
							// 如果是从首页直接过来，尝试从全局变量获取更完整的人脸数据
							const app = getApp();
							let originalFaces = null;
							if (app && app.globalData && app.globalData.analysisResult && 
								app.globalData.analysisResult.data && 
								app.globalData.analysisResult.data.faces) {
								originalFaces = app.globalData.analysisResult.data.faces;
								console.log('从全局变量获取到原始人脸数据:', JSON.stringify(originalFaces));
							}
							
							for (let i = 0; i < analysisResult.face_rectangles.length; i++) {
								// 从原始数据中获取，不再随机生成差异
								let faceBeauty, faceGender, faceAge;
								
								// 1. 首先尝试使用数据库中保存的完整人脸数据
								if (hasSavedFacesData) {
									faceBeauty = analysisResult.faces_data[i].Beauty;
									faceGender = analysisResult.faces_data[i].Gender;
									faceAge = analysisResult.faces_data[i].Age;
									console.log(`使用数据库保存的完整数据 #${i+1}:`, {beauty: faceBeauty, gender: faceGender, age: faceAge});
								}
								// 2. 其次尝试使用全局变量中的数据
								else if (originalFaces && originalFaces[i]) {
									// 使用原始分析数据
									faceBeauty = originalFaces[i].Beauty;
									faceGender = originalFaces[i].Gender;
									faceAge = originalFaces[i].Age;
									console.log(`使用全局变量数据 #${i+1}:`, {beauty: faceBeauty, gender: faceGender, age: faceAge});
								} else {
									// 3. 最后使用当前主脸数据，不添加随机差异
									faceBeauty = this.beauty;
									faceGender = this.gender;
									faceAge = this.age;
									console.log(`使用主脸数据 #${i+1} (无随机差异):`, {beauty: faceBeauty, gender: faceGender, age: faceAge});
								}
								
								this.facesData.push({
									Beauty: faceBeauty,
									Gender: faceGender,
									Age: faceAge,
									FaceRectangle: analysisResult.face_rectangles[i]
								});
							}
							
							// 多人脸情况下，使用第一个人脸作为主数据，不计算平均值
							if (this.facesData.length > 1) {
								console.log('多人脸情况下使用第一个人脸作为主数据，不计算平均值');
								const firstFace = this.facesData[0];
								console.log('第一个人脸数据:', JSON.stringify(firstFace));
								this.beauty = firstFace.Beauty;
								this.gender = firstFace.Gender;
								this.age = firstFace.Age;
								this.score = this.beauty;
							}
						} else {
							// 没有存储位置信息，创建默认位置
							console.log('没有存储位置信息，创建默认位置');
							for (let i = 0; i < faceCount; i++) {
								// 使用相同的数据，不添加随机差异
								let faceBeauty = this.beauty;
								let faceGender = this.gender;
								let faceAge = this.age;
								
								console.log(`创建默认位置的人脸 #${i+1} (无随机差异):`, {beauty: faceBeauty, gender: faceGender, age: faceAge});
								
								// 为多人脸创建不同位置的人脸框
								let faceRect = {
									top: 100,
									left: 100,
									width: 150,
									height: 150
								};
								
								// 如果是多人脸，水平排列
								if (faceCount > 1) {
									const spacing = 300 / faceCount;
									faceRect.left = 50 + i * spacing;
								}
								
								this.facesData.push({
									Beauty: faceBeauty,
									Gender: faceGender,
									Age: faceAge,
									FaceRectangle: faceRect
								});
							}
							
							// 多人脸情况下，使用第一个人脸作为主数据，不计算平均值
							if (this.facesData.length > 1) {
								console.log('多人脸情况下使用第一个人脸作为主数据，不计算平均值');
								const firstFace = this.facesData[0];
								console.log('第一个人脸数据:', JSON.stringify(firstFace));
								this.beauty = firstFace.Beauty;
								this.gender = firstFace.Gender;
								this.age = firstFace.Age;
								this.score = this.beauty;
							}
						}
						
						// 打印最终生成的人脸数据
						console.log('最终生成的人脸数据:', JSON.stringify(this.facesData));
						
						// 强制更新视图，确保计算属性重新计算
						this.$nextTick(() => {
							console.log('强制更新视图，确保分析数据显示正确');
							// 打印当前计算属性结果，用于验证
							console.log('当前分析项:', JSON.stringify(this.analysisItems));
							console.log('是否多人脸:', this.isMultiFace);
							console.log('趣味评语:', this.funnyComment);
							
							// 强制更新
							this.$forceUpdate();
						});
						
						// 获取图片信息
						setTimeout(() => {
							uni.getImageInfo({
								src: this.imageUrl,
								success: (res) => {
									console.log('获取图片信息成功:', res);
									this.originalWidth = res.width;
									this.originalHeight = res.height;
									
									// 图片加载后重新计算位置
									this.getRenderedImageSize();
								},
								fail: (err) => {
									console.error('获取图片信息失败:', err);
									// 使用默认值
									this.originalWidth = 300;
									this.originalHeight = 400;
								}
							});
						}, 300);
					} else {
						console.error('记录中没有分析结果');
						this.useMockData();
					}
				} else {
					console.error('未找到记录，ID:', id);
					this.useMockData();
				}
			} catch (error) {
				console.error('从数据库加载数据失败:', error);
				this.useMockData();
			}
		}
	}
}
</script>

<style lang="scss">
/* 页面特定样式 */

.preview-image {
	
}

.score-badge {
	position: absolute;
	color: white;
	border-radius: 20px; /* 减小圆角 */
	box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
	z-index: 11;
	padding: 0 10px; /* 减小内边距 */
	height: 30px; /* 减小高度 */
	transform-origin: center bottom;
}

.badge-content {
	height: 100%;
	display: flex;
	align-items: center;
}

/* 减小图标尺寸 */
.gender-icon-image {
	width: 20px;  
	height: 20px; 
	margin-right: 3px;
}

/* 减小分数文字大小 */
.score-text {
	font-size: 16px;
	font-weight: bold;
}

/* 男性样式 */
.male-style {
	background-color: #68b1ff;
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

/* 女性样式 */
.female-style {
	background-color: #ff6a86;
	box-shadow: 0 4px 12px rgba(255, 45, 85, 0.4);
}

.analysis-list {
	margin-top: 30px;
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 12px;
}

.analysis-item {
	background-color: transparent;
	border-radius: 18px;
	padding: 8px 16px;
	display: inline-flex;
	align-items: center;
	border: 1px solid var(--border-color);
	margin-bottom: 10px;
}

.item-label {
	color: var(--text-color-secondary);
	font-size: 14px;
	margin-right: 8px;
}

.item-value {
	color: var(--text-color);
	font-weight: bold;
	font-size: 16px;
}

/* 夸张评语样式 */
.funny-comment {
	text-align: center;
	// margin: 15px auto 10px;
	padding: 0 20px;
	font-size: 18px;
	font-weight: bold;
	line-height: 1.5;
	border-radius: 12px;
	padding: 12px;
}

/* 调整标签间距，添加个性标签专用样式 */
.analysis-item {
	margin-bottom: 10px;
}

/* 个性标签特殊样式 */
.analysis-item:last-child .item-value {
	font-weight: bold;
	font-size: 17px;
}

/* 性别样式区分 */
.male-style:last-child .item-value {
	color: #007AFF;
}

.female-style:last-child .item-value {
	color: #FF2D55;
}

/* 高分评语样式 - 使用暖色调 */
.high-score-comment {
	color: #FF9500;
	background-color: rgba(255, 149, 0, 0.1);
	border-radius: 12px;
	padding: 12px;
}

/* 低分评语样式 - 使用冷色调但保持幽默感 */
.low-score-comment {
	color: #5856D6;
	background-color: rgba(88, 86, 214, 0.1);
	border-radius: 12px;
	padding: 12px;
}

/* 人脸框相关样式 */
.image-container {
	position: relative;
	width: 100%;
	display: flex;
	justify-content: center;
	//overflow: hidden;
}

.preview-image {
	max-width: 100%;
	max-height: 300px;
	border-radius: 12px;
}

.face-canvas {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

.face-rectangle {
	position: absolute;
	border: 2px solid #ffffff;
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
	border-radius: 4px;
	pointer-events: none;
	z-index: 10;
}


/* 人脸框和评分样式 */
.face-frame-container {
  position: relative;
  margin: 20px auto;
  width: 100%;
  max-width: 400px;
  // height: 400px; /* 固定高度确保容器有空间 */
  // overflow: hidden;
  text-align: center;
}

.face-frame {
  position: absolute;
  border: 2px solid #FFFFFF;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  z-index: 2;
  pointer-events: none;
  /* 增加细线边框让框更精确 */
  outline: 1px solid rgba(255, 128, 171, 0.5);
  box-sizing: border-box;
}

/* 添加多人脸相关样式 */
.multi-face-tip {
 text-align: center;
 margin: 10px 0;
 font-size: 14px;
 color: var(--text-color-secondary);
}

/* 使用CSS类定义不同大小的徽章 */
.badge-small {
	transform: scale(0.7) translate(-50%, 0) !important;
	padding: 0 14px !important;
	height: 36px !important;
}

.badge-medium {
	/* 默认尺寸 */
}

.badge-large {
	transform: scale(1.2) translate(-50%, 0) !important;
}

/* 如果有多个人脸，所有徽章稍微缩小 */
.multi-face-mode .score-badge {
	transform: scale(0.85) translate(-50%, 0) !important;
}

/* 保存状态提示样式 */
.save-status {
	text-align: center;
	margin: 10px 0;
	font-size: 14px;
	color: var(--text-color-secondary);
}
</style> 