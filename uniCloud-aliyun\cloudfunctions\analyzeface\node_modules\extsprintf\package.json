{"_from": "extsprintf@1.3.0", "_id": "extsprintf@1.3.0", "_inBundle": false, "_integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==", "_location": "/extsprintf", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "extsprintf@1.3.0", "name": "extsprintf", "escapedName": "extsprintf", "rawSpec": "1.3.0", "saveSpec": null, "fetchSpec": "1.3.0"}, "_requiredBy": ["/jsprim", "/verror"], "_resolved": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.3.0.tgz", "_shasum": "96918440e3041a7a414f8c52e3c574eb3c3e1e05", "_spec": "extsprintf@1.3.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\jsprim", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "bundleDependencies": false, "deprecated": false, "description": "extended POSIX-style sprintf", "engines": ["node >=0.6.0"], "homepage": "https://github.com/davepacheco/node-extsprintf#readme", "license": "MIT", "main": "./lib/extsprintf.js", "name": "extsprintf", "repository": {"type": "git", "url": "git://github.com/davepacheco/node-extsprintf.git"}, "version": "1.3.0"}