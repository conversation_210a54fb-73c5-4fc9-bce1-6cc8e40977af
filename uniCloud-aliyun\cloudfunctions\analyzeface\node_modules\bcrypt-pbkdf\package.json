{"_from": "bcrypt-pbkdf@^1.0.0", "_id": "bcrypt-pbkdf@1.0.2", "_inBundle": false, "_integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "_location": "/bcrypt-pbkdf", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "bcrypt-pbkdf@^1.0.0", "name": "bcrypt-pbkdf", "escapedName": "bcrypt-pbkdf", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/sshpk"], "_resolved": "https://registry.npmmirror.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "_shasum": "a4301d389b6a43f9b67ff3ca11a3f6637e360e9e", "_spec": "bcrypt-pbkdf@^1.0.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\sshpk", "bugs": {"url": "https://github.com/joyent/node-bcrypt-pbkdf/issues"}, "bundleDependencies": false, "dependencies": {"tweetnacl": "^0.14.3"}, "deprecated": false, "description": "Port of the OpenBSD bcrypt_pbkdf function to pure JS", "devDependencies": {}, "homepage": "https://github.com/joyent/node-bcrypt-pbkdf#readme", "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "name": "bcrypt-pbkdf", "repository": {"type": "git", "url": "git://github.com/joyent/node-bcrypt-pbkdf.git"}, "version": "1.0.2"}