'use strict';
// 引入请求模块
const request = require('request-promise');

// Face++配置信息
const config = {
    apiKey: 'TPAXNLxI0FF2OFEHAR5qEqGk9wK78ysC',
    apiSecret: 'MZLhoIW5f0e7QrvAU9A0nKQeoliA7wXs',
    detectUrl: 'https://api-cn.faceplusplus.com/facepp/v3/detect'
};

// 在云函数中添加base64清理函数
function cleanBase64(base64String) {
    // 1. 移除可能存在的data URI前缀
    if (base64String.includes('data:image')) {
        const commaIndex = base64String.indexOf(',');
        if (commaIndex !== -1) {
            base64String = base64String.substring(commaIndex + 1);
        }
    }
    
    // 2. 移除所有空格、换行符和其他非base64字符
    base64String = base64String.replace(/[\r\n\s]+/g, '');
    
    // 3. 确保字符串长度是4的倍数，必要时添加填充
    const remainder = base64String.length % 4;
    if (remainder > 0) {
        base64String += '='.repeat(4 - remainder);
    }
    
    return base64String;
}

exports.main = async (event, context) => {
    console.log('收到请求，fileContent长度：', event.fileContent ? event.fileContent.length : 0);
    
    try {
        // 验证配置
        if (!config.apiKey || !config.apiSecret) {
            throw new Error('未配置Face++密钥');
        }
        
        // 验证参数
        if (!event.fileContent) {
            throw new Error('未接收到图片数据');
        }
        
        // 图片大小检查
        if (event.fileContent.length > 2000000) { // 大约2MB
            throw new Error('图片太大，请使用更小的图片');
        }
        
        // 清理和处理base64字符串
        let imageBase64 = event.fileContent ? cleanBase64(event.fileContent) : '';
        
        console.log('处理后的base64长度:', imageBase64.length);
        
        // 设置较短的超时时间
        const detectOptions = {
            method: 'POST',
            url: config.detectUrl,
            formData: {
                api_key: config.apiKey,
                api_secret: config.apiSecret,
                image_base64: imageBase64,
                return_attributes: 'gender,age,beauty,emotion,smiling'
            },
            json: true,
            timeout: 25000 // 25秒超时
        };
        
        // 调用Face++ API
        console.log('开始调用Face++ API...');
        const detectResult = await request(detectOptions);
        
        console.log('API返回，检测到人脸数量:', detectResult.faces ? detectResult.faces.length : 0);
        
        // 检查是否检测到人脸
        if (!detectResult.faces || detectResult.faces.length === 0) {
            throw new Error('NO_FACE_FOUND: 未检测到人脸，请尝试其他照片');
        }
        
        // 处理所有人脸数据
        const facesData = detectResult.faces.map(face => {
            const attributes = face.attributes || {};

            // 根据性别选择对应的颜值分数，并进行适当的分数调整
            let beautyScore = 75; // 默认分数
            if (attributes.beauty) {
                const isMale = attributes.gender && attributes.gender.value === 'Male';
                // 根据性别选择对应分数
                const rawScore = isMale ? attributes.beauty.male_score : attributes.beauty.female_score;

                // 对分数进行调整，提升整体分数区间
                // Face++ 原始分数通常在 20-80 区间，我们将其映射到 40-95 区间
                beautyScore = Math.round(Math.min(95, Math.max(40, rawScore * 1.2 + 15)));

                // 添加调试日志
                console.log(`颜值分数调整: 性别=${isMale ? '男' : '女'}, 原始分数=${rawScore}, 调整后=${beautyScore}`);
            }

            return {
                Beauty: beautyScore,
                Gender: Math.round(attributes.gender ?
                    (attributes.gender.value === 'Male' ? 75 : 25) : 50),
                Age: Math.round(attributes.age ? attributes.age.value : 25),
                FaceRectangle: face.face_rectangle // 确保包含人脸坐标
            };
        });
        
        // 打印人脸框坐标信息，进行调试
        console.log('第一个人脸框坐标:', JSON.stringify(facesData[0].FaceRectangle));
        
        // 返回所有人脸数据和单个人脸数据(兼容现有处理逻辑)
            return {
                code: 0,
            data: {
                // 单个人脸数据(取第一个)
                Beauty: facesData[0].Beauty,
                Gender: facesData[0].Gender,
                Age: facesData[0].Age,
                FaceRectangle: facesData[0].FaceRectangle,
                // 所有人脸
                faces: facesData,
                faceCount: facesData.length
            }
            };
        
    } catch (error) {
        console.error('云函数执行错误:', error);
        
        // 分析错误类型提供更具体的错误信息
        let errorMessage = error.message || '分析失败';
        
        if (error.message.includes('TIMEOUT')) {
            errorMessage = '分析超时，请使用更小的图片';
        } else if (error.message.includes('IMAGE_ERROR')) {
            errorMessage = '图片格式不支持，请使用JPG或PNG格式';
        }
        
        return {
            code: -1,
            message: errorMessage,
            error: error.toString()
        };
    }
}; 