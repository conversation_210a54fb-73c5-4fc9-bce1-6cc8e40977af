{"name": "core-util-is", "version": "1.0.2", "description": "The `util.is*` functions introduced in Node v0.12.", "main": "lib/util.js", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is"}, "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "MIT", "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "^2.3.0"}, "_resolved": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.2.tgz", "_integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "_from": "core-util-is@1.0.2"}