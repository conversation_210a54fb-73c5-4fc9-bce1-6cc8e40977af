{"_from": "aws-sign2@~0.7.0", "_id": "aws-sign2@0.7.0", "_inBundle": false, "_integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==", "_location": "/aws-sign2", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "aws-sign2@~0.7.0", "name": "aws-sign2", "escapedName": "aws-sign2", "rawSpec": "~0.7.0", "saveSpec": null, "fetchSpec": "~0.7.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/aws-sign2/-/aws-sign2-0.7.0.tgz", "_shasum": "b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8", "_spec": "aws-sign2@~0.7.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "bugs": {"url": "https://github.com/mikeal/aws-sign/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "AWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.", "devDependencies": {}, "engines": {"node": "*"}, "homepage": "https://github.com/mikeal/aws-sign#readme", "license": "Apache-2.0", "main": "index.js", "name": "aws-sign2", "optionalDependencies": {}, "repository": {"url": "git+https://github.com/mikeal/aws-sign.git"}, "version": "0.7.0"}