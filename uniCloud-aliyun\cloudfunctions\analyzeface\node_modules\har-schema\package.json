{"_from": "har-schema@^2.0.0", "_id": "har-schema@2.0.0", "_inBundle": false, "_integrity": "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==", "_location": "/har-schema", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "har-schema@^2.0.0", "name": "har-schema", "escapedName": "har-schema", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/har-validator"], "_resolved": "https://registry.npmmirror.com/har-schema/-/har-schema-2.0.0.tgz", "_shasum": "a94c2224ebcac04782a0d9035521f24735b7ec92", "_spec": "har-schema@^2.0.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\har-validator", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "bugs": {"url": "https://github.com/ahmadnassri/har-schema/issues"}, "bundleDependencies": false, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "JSON Schema for HTTP Archive (HAR)", "devDependencies": {"ajv": "^5.0.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}, "engines": {"node": ">=4"}, "files": ["lib"], "homepage": "https://github.com/ahmadnassri/har-schema", "keywords": ["har", "http", "archive", "JSON", "schema", "JSON-schema"], "license": "ISC", "main": "lib/index.js", "name": "har-schema", "repository": {"type": "git", "url": "git+https://github.com/ahmadnassri/har-schema.git"}, "scripts": {"codeclimate": "tap --coverage-report=text-lcov | codeclimate-test-reporter", "coverage": "tap test --reporter silent --coverage", "pretest": "snazzy && echint", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "test": "tap test --reporter spec"}, "version": "2.0.0"}