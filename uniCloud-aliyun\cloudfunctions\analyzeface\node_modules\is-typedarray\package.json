{"_from": "is-typedarray@~1.0.0", "_id": "is-typedarray@1.0.0", "_inBundle": false, "_integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==", "_location": "/is-typedarray", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-typedarray@~1.0.0", "name": "is-typedarray", "escapedName": "is-typedarray", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/is-typedarray/-/is-typedarray-1.0.0.tgz", "_shasum": "e479c80858df0c1b11ddda6940f96011fcda4a9a", "_spec": "is-typedarray@~1.0.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/"}, "bugs": {"url": "https://github.com/hughsk/is-typedarray/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Detect whether or not an object is a Typed Array", "devDependencies": {"tape": "^2.13.1"}, "homepage": "https://github.com/hughsk/is-typedarray", "keywords": ["typed", "array", "detect", "is", "util"], "license": "MIT", "main": "index.js", "name": "is-typedarray", "repository": {"type": "git", "url": "git://github.com/hughsk/is-typedarray.git"}, "scripts": {"test": "node test"}, "version": "1.0.0"}