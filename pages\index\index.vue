<template>
	<view class="container">
		<view class="page-title">AI颜值测试</view>
		
		<!-- 只保留上传区域，完全删除预览相关内容 -->
		<view class="upload-box">
			<view class="upload-area" @tap="chooseAndAnalyze">
				<image class="upload-icon" src="/static/images/camera.png"></image>
				<text class="upload-text">点击上传照片或拍照</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				analyzing: false,
				maxImageSize: 5 * 1024 * 1024  // 5MB
			}
		},
		onLoad() {
			// 添加全局未捕获异常处理
			window.addEventListener('unhandledrejection', event => {
				console.error('未捕获的Promise错误:', event.reason);
			});
			
			// 页面加载时重置状态
			this.analyzing = false
			
			// 检查服务空间是否正确初始化
			// 可以使用analyzeface云函数调用来验证连接
		},
		methods: {
			// 选择并分析图片
			chooseAndAnalyze() {
				// 避免重复操作
				if (this.analyzing) {
					return;
				}
				
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'], 
					sourceType: ['album', 'camera'],
					success: async (res) => {
						const tempFile = res.tempFiles[0];
						const imageUrl = res.tempFilePaths[0];
						
						// 检查文件大小
						if (tempFile.size > this.maxImageSize) {
							uni.showToast({
								title: '图片不能超过5MB，请重新选择',
								icon: 'none',
								duration: 2000
							});
							return;
						}
						
						// 显示加载
						this.analyzing = true;
						uni.showLoading({
							title: '分析中...'
						});
						
						try {
							// 1. 先上传图片到云存储
							let cloudFileID = '';
							try {
								uni.showLoading({ title: '上传图片中...' });
								
								// 生成随机文件名
								const fileName = 'face_' + Date.now() + '.jpg';
								
								const uploadResult = await uniCloud.uploadFile({
									filePath: imageUrl,
									cloudPath: fileName
								});
								
								console.log('图片上传成功:', uploadResult);
								cloudFileID = uploadResult.fileID;
							} catch (uploadErr) {
								console.error('上传图片失败:', uploadErr);
								throw new Error('上传图片失败，请重试');
							}
							
							// 2. 读取图片内容
							const fileContent = await new Promise((resolve, reject) => {
								const fs = uni.getFileSystemManager();
								fs.readFile({
									filePath: imageUrl,
									encoding: 'base64',
									success: (res) => resolve(res.data),
									fail: (err) => reject(new Error('读取图片失败'))
								});
							});
							
							// 3. 调用云函数分析人脸
							const result = await uniCloud.callFunction({
								name: 'analyzeface',
								data: { fileContent }
							});
							
							if (result.result && result.result.code === 0) {
								// 4. 保存分析结果和云文件ID到数据库
								const dbData = {
									cloud_file_id: cloudFileID,
									cloud_file_name: cloudFileID.substring(cloudFileID.lastIndexOf('/') + 1),
									analysis_result: {
										score: Math.round(result.result.data.Beauty || 75),
										age: Math.round(result.result.data.Age || 25),
										gender: result.result.data.Gender, // 保持原始数值，不转换为字符串
										faces_count: result.result.data.faces ? result.result.data.faces.length : 1,
										// 保存人脸位置信息
										face_rectangles: result.result.data.faces ? 
											result.result.data.faces.map(face => face.FaceRectangle) : 
											[result.result.data.FaceRectangle],
										// 添加保存完整的人脸数据，包括每个人脸的颜值、性别、年龄
										faces_data: result.result.data.faces ? 
											result.result.data.faces.map(face => ({
												Beauty: face.Beauty,
												Gender: face.Gender,
												Age: face.Age
											})) : 
											[{
												Beauty: result.result.data.Beauty,
												Gender: result.result.data.Gender,
												Age: result.result.data.Age
											}]
									},
									create_date: Date.now()
								};
								
								// 打印保存到数据库的数据，方便调试
								console.log('保存到数据库的数据:', JSON.stringify(dbData));
								
								const db = uniCloud.database();
								const addResult = await db.collection('face_analysis').add(dbData);
								console.log(addResult);
								
								console.log('数据已保存到数据库，ID:', addResult.result.id);
								
								// 5. 保存结果到全局变量，并包含数据库记录ID
								getApp().globalData.analysisResult = {
									imageUrl: cloudFileID,
									data: result.result.data,
									recordId: addResult.result.id // 保存数据库记录ID
								};
								
								// 6. 跳转到结果页，并传递记录ID
								uni.redirectTo({
									url: `/pages/result/index?id=${addResult.result.id}`
								});
							} else {
								const errorMsg = result.result ? result.result.message : '分析返回结果异常';
								throw new Error(errorMsg);
							}
						} catch (error) {
							uni.showToast({
								title: error.message || '分析失败，请重试',
								icon: 'none',
								duration: 3000
							});
						} finally {
							this.analyzing = false;
							uni.hideLoading();
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	/* 页面特定样式 */
	.upload-box {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 20px;
	}

	.upload-area {
		width: 300px;
		height: 300px;
		background-color: var(--card-background);
		border: 2px dashed var(--border-color);
		border-radius: 12px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		cursor: pointer;
	}

	.upload-icon {
		width: 64px;
		height: 64px;
		margin-bottom: 16px;
	}

	.upload-text {
		color: var(--text-color-secondary);
		font-size: 16px;
	}

	/* 在宽度较小的屏幕上，按钮垂直排列 */
	@media screen and (max-width: 375px) {
		.preview-actions {
			flex-direction: column;
		}
	}
</style>
