{"_from": "getpass@^0.1.1", "_id": "getpass@0.1.7", "_inBundle": false, "_integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "_location": "/getpass", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "getpass@^0.1.1", "name": "getpass", "escapedName": "getpass", "rawSpec": "^0.1.1", "saveSpec": null, "fetchSpec": "^0.1.1"}, "_requiredBy": ["/sshpk"], "_resolved": "https://registry.npmmirror.com/getpass/-/getpass-0.1.7.tgz", "_shasum": "5eff8e3e684d569ae4cb2b1282604e8ba62149fa", "_spec": "getpass@^0.1.1", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\sshpk", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/arekinath/node-getpass/issues"}, "bundleDependencies": false, "dependencies": {"assert-plus": "^1.0.0"}, "deprecated": false, "description": "getpass for node.js", "homepage": "https://github.com/arekinath/node-getpass#readme", "license": "MIT", "main": "lib/index.js", "name": "getpass", "repository": {"type": "git", "url": "git+https://github.com/arekinath/node-getpass.git"}, "scripts": {"test": "tape test/*.test.js"}, "version": "0.1.7"}