{"_from": "http-signature@~1.2.0", "_id": "http-signature@1.2.0", "_inBundle": false, "_integrity": "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==", "_location": "/http-signature", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-signature@~1.2.0", "name": "http-signature", "escapedName": "http-signature", "rawSpec": "~1.2.0", "saveSpec": null, "fetchSpec": "~1.2.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/http-signature/-/http-signature-1.2.0.tgz", "_shasum": "9aecd925114772f3d95b65a60abb8f7c18fbace1", "_spec": "http-signature@~1.2.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "Joyent, Inc"}, "bugs": {"url": "https://github.com/joyent/node-http-signature/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "deprecated": false, "description": "Reference implementation of Joyent's HTTP Signature scheme.", "devDependencies": {"tap": "0.4.2", "uuid": "^2.0.2"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}, "homepage": "https://github.com/joyent/node-http-signature/", "keywords": ["https", "request"], "license": "MIT", "main": "lib/index.js", "name": "http-signature", "repository": {"type": "git", "url": "git://github.com/joyent/node-http-signature.git"}, "scripts": {"test": "tap test/*.js"}, "version": "1.2.0"}