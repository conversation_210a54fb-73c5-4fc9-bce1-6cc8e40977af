{"_from": "isstream@~0.1.2", "_id": "isstream@0.1.2", "_inBundle": false, "_integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==", "_location": "/isstream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "isstream@~0.1.2", "name": "isstream", "escapedName": "isstream", "rawSpec": "~0.1.2", "saveSpec": null, "fetchSpec": "~0.1.2"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/isstream/-/isstream-0.1.2.tgz", "_shasum": "47e63f7af55afa6f92e1500e690eb8b8529c099a", "_spec": "isstream@~0.1.2", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/rvagg/isstream/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Determine if an object is a Stream", "devDependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x", "tape": "~2.12.3"}, "homepage": "https://github.com/rvagg/isstream", "keywords": ["stream", "type", "streams", "readable-stream", "hippo"], "license": "MIT", "main": "isstream.js", "name": "isstream", "repository": {"type": "git", "url": "git+https://github.com/rvagg/isstream.git"}, "scripts": {"test": "tar --xform 's/^package/readable-stream-1.0/' -zxf readable-stream-1.0.*.tgz && tar --xform 's/^package/readable-stream-1.1/' -zxf readable-stream-1.1.*.tgz && node test.js; rm -rf readable-stream-1.?/"}, "version": "0.1.2"}