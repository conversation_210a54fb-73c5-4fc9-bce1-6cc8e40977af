{"_from": "aws4@^1.8.0", "_id": "aws4@1.13.2", "_inBundle": false, "_integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==", "_location": "/aws4", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "aws4@^1.8.0", "name": "aws4", "escapedName": "aws4", "rawSpec": "^1.8.0", "saveSpec": null, "fetchSpec": "^1.8.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/aws4/-/aws4-1.13.2.tgz", "_shasum": "0aa167216965ac9474ccfa83892cfb6b3e1e52ef", "_spec": "aws4@^1.8.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mhart"}, "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Signs and prepares requests using AWS Signature Version 4", "devDependencies": {"mocha": "^10.7.3", "should": "^13.2.3"}, "files": ["aws4.js", "lru.js"], "homepage": "https://github.com/mhart/aws4#readme", "license": "MIT", "main": "aws4.js", "name": "aws4", "repository": {"type": "git", "url": "git+https://github.com/mhart/aws4.git"}, "scripts": {"integration": "node ./test/slow.js", "test": "mocha ./test/fast.js -R list"}, "version": "1.13.2"}