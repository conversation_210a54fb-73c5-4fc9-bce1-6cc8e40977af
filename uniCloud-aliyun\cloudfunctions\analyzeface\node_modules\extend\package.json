{"_from": "extend@~3.0.2", "_id": "extend@3.0.2", "_inBundle": false, "_integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "_location": "/extend", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "extend@~3.0.2", "name": "extend", "escapedName": "extend", "rawSpec": "~3.0.2", "saveSpec": null, "fetchSpec": "~3.0.2"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz", "_shasum": "f8b1136b4071fbd8eb140aff858b1019ec2915fa", "_spec": "extend@~3.0.2", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "dependencies": {}, "deprecated": false, "description": "Port of jQuery.extend for node.js and the browser", "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.19.1", "jscs": "^3.0.7", "tape": "^4.9.1"}, "homepage": "https://github.com/justmoon/node-extend#readme", "keywords": ["extend", "clone", "merge"], "license": "MIT", "main": "index", "name": "extend", "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "scripts": {"coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "eslint": "eslint *.js */*.js", "jscs": "jscs *.js */*.js", "lint": "npm run jscs && npm run eslint", "posttest": "npm run coverage-quiet", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node test"}, "version": "3.0.2"}