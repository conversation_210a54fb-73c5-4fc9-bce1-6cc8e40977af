{"_from": "jsprim@^1.2.2", "_id": "jsprim@1.4.2", "_inBundle": false, "_integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "_location": "/jsprim", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "jsprim@^1.2.2", "name": "jsprim", "escapedName": "jsprim", "rawSpec": "^1.2.2", "saveSpec": null, "fetchSpec": "^1.2.2"}, "_requiredBy": ["/http-signature"], "_resolved": "https://registry.npmmirror.com/jsprim/-/jsprim-1.4.2.tgz", "_shasum": "712c65533a15c878ba59e9ed5f0e26d5b77c5feb", "_spec": "jsprim@^1.2.2", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\http-signature", "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "bundleDependencies": false, "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "deprecated": false, "description": "utilities for primitive JavaScript types", "engines": {"node": ">=0.6.0"}, "homepage": "https://github.com/joyent/node-jsprim#readme", "license": "MIT", "main": "./lib/jsprim.js", "name": "jsprim", "repository": {"type": "git", "url": "git://github.com/joyent/node-jsprim.git"}, "version": "1.4.2"}