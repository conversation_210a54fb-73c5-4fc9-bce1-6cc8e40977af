{"_from": "jsbn@~0.1.0", "_id": "jsbn@0.1.1", "_inBundle": false, "_integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==", "_location": "/jsbn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "jsbn@~0.1.0", "name": "jsbn", "escapedName": "jsbn", "rawSpec": "~0.1.0", "saveSpec": null, "fetchSpec": "~0.1.0"}, "_requiredBy": ["/ecc-jsbn", "/sshpk"], "_resolved": "https://registry.npmmirror.com/jsbn/-/jsbn-0.1.1.tgz", "_shasum": "a5e654c2e5a2deb5f201d96cefbca80c0ef2f513", "_spec": "jsbn@~0.1.0", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\sshpk", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/andyperlitch/jsbn/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The jsbn library is a fast, portable implementation of large-number math in pure JavaScript, enabling public-key crypto and other applications on desktop and mobile browsers.", "homepage": "https://github.com/andyperlitch/jsbn#readme", "keywords": ["biginteger", "bignumber", "big", "integer"], "license": "MIT", "main": "index.js", "name": "jsbn", "repository": {"type": "git", "url": "git+https://github.com/andyperlitch/jsbn.git"}, "scripts": {"test": "mocha test.js"}, "version": "0.1.1"}