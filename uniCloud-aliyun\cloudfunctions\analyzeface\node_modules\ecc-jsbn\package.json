{"_from": "ecc-jsbn@~0.1.1", "_id": "ecc-jsbn@0.1.2", "_inBundle": false, "_integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "_location": "/ecc-jsbn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ecc-jsbn@~0.1.1", "name": "ecc-jsbn", "escapedName": "ecc-jsbn", "rawSpec": "~0.1.1", "saveSpec": null, "fetchSpec": "~0.1.1"}, "_requiredBy": ["/sshpk"], "_resolved": "https://registry.npmmirror.com/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "_shasum": "3a83a904e54353287874c564b7549386849a98c9", "_spec": "ecc-jsbn@~0.1.1", "_where": "D:\\workspace\\iface\\uniCloud-aliyun\\cloudfunctions\\analyzeface\\node_modules\\sshpk", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, "bugs": {"url": "https://github.com/quartzjer/ecc-jsbn/issues"}, "bundleDependencies": false, "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}, "deprecated": false, "description": "ECC JS code based on JSBN", "homepage": "https://github.com/quartzjer/ecc-jsbn", "keywords": ["jsbn", "ecc", "browserify"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, {"name": "<PERSON>", "url": "https://github.com/rynomad"}], "name": "ecc-jsbn", "repository": {"type": "git", "url": "git+https://github.com/quartzjer/ecc-jsbn.git"}, "version": "0.1.2"}